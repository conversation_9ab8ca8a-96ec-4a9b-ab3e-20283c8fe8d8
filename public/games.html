<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择游戏</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body id="page-games">
    <div class="stars-bg"></div>
    <div class="container">
        <header>
            <h1>游戏选择</h1>
            <p>请勾选您想要安装的游戏<br>128G卡5-8个游戏。256G卡20-30个游戏。512G卡50个左右。<br>以实际空间为准</p>
        </header>
        
        <div class="content-wrapper">
            <div class="main-content">
                <div class="toolbar">
                    <input type="search" id="search-bar" placeholder="搜索中文或英文游戏名...">
                    <span id="selection-counter" class="selection-counter">已选择 0 个游戏</span>
                </div>

                <form id="game-selection-form">
                    <div id="game-list-container">
                        <!-- Game items will be inserted here by JavaScript -->
                    </div>
                    <div id="pagination-controls" class="pagination-controls">
                        <!-- Pagination buttons will be inserted here by JavaScript -->
                    </div>
                    <div class="submission-area">
                         <p>确认无误后，请点击下方按钮提交您的选择。</p>
                         <button type="submit" id="submit-selection" class="btn">确认提交</button>
                         <p id="form-message" class="message-area"></p>
                    </div>
                </form>
            </div>
            <!-- 购物车样式的已选游戏 -->
            <div id="game-cart" class="game-cart">
                <div class="cart-icon" id="cart-toggle">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                    </svg>
                    <span class="cart-count" id="cart-count">0</span>
                </div>
                <div class="cart-dropdown" id="cart-dropdown">
                    <div class="cart-header">已选游戏</div>
                    <div class="cart-items" id="cart-items">
                        <div class="empty-cart">尚未选择任何游戏</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
