const express = require('express');
const xlsx = require('xlsx');
const path = require('path');
const cors = require('cors');
const fs = require('fs');
const os = require('os');

const app = express();
const port = 8989;

// --- Middleware Setup ---
// 1. CORS for cross-origin requests
app.use(cors());

// 2. Body Parsers - crucial for reading req.body
// Use express's built-in parsers. This is the modern standard.
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 3. Serve static files AFTER middleware
app.use(express.static(path.join(__dirname, 'public')));

// Endpoint to handle form submission
app.post('/api/register', (req, res) => {
    const { phone, serial, order } = req.body;

    // Basic validation
    if (!phone || !serial || !order) {
        return res.status(400).json({ success: false, message: '请填写所有必填项。' });
    }

    // Here you can add more complex validation, e.g., check if the order number is valid
    console.log('接收到新的登记信息:');
    console.log('  手机号:', phone);
    console.log('  机器序列号:', serial);
    console.log('  订单号:', order);
    
    // For now, we'll just return success.
    res.json({ success: true, message: '登记成功，正在跳转到游戏选择页面...' });
});

// Endpoint to get the list of games
app.get('/api/games', (req, res) => {
    console.log('--- [DIAGNOSTIC] ---');
    console.log(`[${new Date().toISOString()}] Received request for /api/games.`);
    try {
        const filePath = path.join(__dirname, '分类合集.xlsx');
        console.log(`[DIAGNOSTIC] Attempting to read Excel file from: ${filePath}`);

        const workbook = xlsx.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const games = xlsx.utils.sheet_to_json(worksheet);
        
        console.log(`[DIAGNOSTIC] Successfully parsed Excel file. Found ${games.length} raw rows.`);

        // Use the correct column names found in the Excel file
        const gameList = games.map(game => ({
            chineseName: game['游戏中文名称'] || '',
            englishName: game['英文名'] || '',
            cover: game['fengmian'] || '',
            link: game['游戏链接'] || '' 
        })).filter(game => game.chineseName || game.englishName);

        console.log(`[DIAGNOSTIC] Processed data. Returning ${gameList.length} valid games to frontend.`);
        console.log('--- [END DIAGNOSTIC] ---');
        res.json(gameList);

    } catch (error) {
        console.error('--- [CRITICAL ERROR] ---');
        console.error(`[${new Date().toISOString()}] 读取Excel文件时出错:`, error);
        console.error('--- [END ERROR] ---');
        res.status(500).json({ success: false, message: '无法加载游戏列表，请联系管理员。' });
    }
});

// Endpoint to handle game selection submission
app.post('/submit', (req, res) => {
    // Correctly destructure the incoming body which now has a userInfo object
    const { userInfo, selectedGames } = req.body;
    
    // --- Detailed Logging for Debugging ---
    console.log('--- New Submission Request Received ---');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Received userInfo:', userInfo);
    console.log('Received selectedGames:', selectedGames);

    // --- Directory for submissions ---
    const submissionsDir = path.join(__dirname, 'submissions');
    if (!fs.existsSync(submissionsDir)) {
        fs.mkdirSync(submissionsDir, { recursive: true });
    }

    const mainWorkbookPath = path.join(__dirname, '分类合集.xlsx');

    if (!userInfo || !selectedGames || selectedGames.length === 0) {
        console.log('Validation failed. Reason: Missing userInfo or selectedGames.');
        return res.status(400).json({ success: false, message: '提交数据不完整。' });
    }

    try {
        console.log('Validation passed. Proceeding to create file...');
        // --- 1. Get Game-to-Link Map ---
        const workbook = xlsx.readFile(mainWorkbookPath);
        const mainSheetName = workbook.SheetNames[0];
        const mainWorksheet = workbook.Sheets[mainSheetName];
        const allGamesData = xlsx.utils.sheet_to_json(mainWorksheet);
        
        const gameLinkMap = new Map();
        allGamesData.forEach(game => {
            const displayName = `${game['游戏中文名称'] || ''} ${game['英文名'] || ''}`.trim();
            const link = game['游戏链接'] || '';
            if (displayName) {
                gameLinkMap.set(displayName, link);
            }
        });

        // --- 2. Get links for selected games and filter for valid ones ---
        const selectedGameLinks = selectedGames
            .map(game => {
                // 如果selectedGames是对象数组（包含name和link），直接使用link
                if (typeof game === 'object' && game.link) {
                    return game.link;
                }
                // 如果selectedGames是字符串数组（游戏名称），从gameLinkMap获取链接
                return gameLinkMap.get(game);
            })
            .filter(link => link && link.trim() !== '' && link !== '链接未找到'); // Keep only valid, non-empty links

        // --- 3. Prepare content for the .txt file ---
        const fileContent = selectedGameLinks.join(os.EOL); // Use OS-specific End-of-Line character for max compatibility

        // --- 4. Save to a stable file (overwrites previous) ---
        const filename = `${userInfo.serial}.txt`;
        const newFilePath = path.join(submissionsDir, filename);

        fs.writeFileSync(newFilePath, fileContent, 'utf8');

        console.log(`订单文件已更新/创建: ${filename}`);
        res.json({ success: true, message: `成功提交 ${selectedGames.length} 个游戏的选择！` });

    } catch (error) {
        console.error('创建提交文件时出错:', error);
        res.status(500).json({ success: false, message: '提交失败，无法保存数据，请联系管理员。' });
    }
});


app.listen(port, () => {
    console.log(`服务器正在 http://localhost:${port} 上运行`);
});
